#!/usr/bin/env python3
"""
测试完整的嵌入模型配置
验证包含embedding_dim的完整配置生成
"""

def test_complete_embedding_config():
    """测试完整的嵌入模型配置生成"""
    print("=== 完整嵌入模型配置测试 ===\n")
    
    def generate_complete_mcp_config(text_model, embedding_model=None):
        """生成完整的MCP环境变量配置"""
        env_config = {}
        
        # 文本生成模型配置
        env_config["OPENAI_API_KEY"] = text_model.get('api_key', '请在模型配置中设置API Key')
        env_config["MODEL_NAME"] = text_model.get('model_id', 'gpt-4o-mini')
        
        if text_model.get('base_url'):
            env_config["OPENAI_BASE_URL"] = text_model['base_url']
        
        # 嵌入模型配置
        if embedding_model:
            # 如果嵌入模型没有API Key，使用"no-api-key"占位符
            env_config["OPENAI_EMBEDDER_API_KEY"] = embedding_model.get('api_key') or "no-api-key"
            env_config["OPENAI_EMBEDDER_MODEL_ID"] = embedding_model.get('model_id', 'text-embedding-3-small')
            
            # 设置嵌入维度
            model_id = embedding_model.get('model_id', '')
            if 'nomic-embed' in model_id:
                default_dim = '768'
            elif 'bge-m3' in model_id:
                default_dim = '1024'
            elif 'text-embedding-3-large' in model_id:
                default_dim = '3072'
            elif 'text-embedding-3-small' in model_id:
                default_dim = '1536'
            else:
                default_dim = '1536'
            
            env_config["OPENAI_EMBEDDER_DIMENSION"] = embedding_model.get('dimension', default_dim)
            
            # 如果嵌入模型有base_url就使用，否则使用文本生成模型的base_url
            if embedding_model.get('base_url'):
                env_config["OPENAI_EMBEDDER_API_URL"] = embedding_model['base_url']
            elif env_config.get("OPENAI_BASE_URL"):
                env_config["OPENAI_EMBEDDER_API_URL"] = env_config["OPENAI_BASE_URL"]
        else:
            # 没有独立嵌入模型，使用文本生成模型的配置作为回退
            env_config["OPENAI_EMBEDDER_API_KEY"] = env_config["OPENAI_API_KEY"]
            env_config["OPENAI_EMBEDDER_MODEL_ID"] = "text-embedding-3-small"
            env_config["OPENAI_EMBEDDER_DIMENSION"] = "1536"
            if env_config.get("OPENAI_BASE_URL"):
                env_config["OPENAI_EMBEDDER_API_URL"] = env_config["OPENAI_BASE_URL"]
        
        return env_config
    
    # 测试1：nomic-embed模型配置
    print("1. nomic-embed模型配置:")
    text_model = {
        'api_key': 'sk-text-key-123',
        'model_id': 'qwen-plus',
        'base_url': 'https://dashscope.aliyuncs.com/compatible-mode/v1'
    }
    
    nomic_embedding = {
        'api_key': None,  # 没有独立API Key
        'model_id': 'nomic-embed-text',
        'dimension': None,  # 使用默认维度
        'base_url': 'http://localhost:11434/v1'
    }
    
    nomic_config = generate_complete_mcp_config(text_model, nomic_embedding)
    print("   ✅ nomic-embed配置:")
    for key, value in nomic_config.items():
        if 'API_KEY' in key:
            print(f"   {key}={value}")
        else:
            print(f"   {key}={value}")
    
    # 测试2：bge-m3模型配置
    print("\n2. bge-m3模型配置:")
    bge_embedding = {
        'api_key': 'sk-bge-key-456',
        'model_id': 'bge-m3:latest',
        'dimension': '1024',  # 明确指定维度
        'base_url': 'http://*********:11434/v1'
    }
    
    bge_config = generate_complete_mcp_config(text_model, bge_embedding)
    print("   ✅ bge-m3配置:")
    for key, value in bge_config.items():
        if 'API_KEY' in key:
            print(f"   {key}={value}")
        else:
            print(f"   {key}={value}")
    
    # 测试3：OpenAI官方嵌入模型
    print("\n3. OpenAI官方嵌入模型:")
    openai_text_model = {
        'api_key': 'sk-openai-123',
        'model_id': 'gpt-4o-mini',
        'base_url': None
    }
    
    openai_embedding = {
        'api_key': 'sk-openai-embed-456',
        'model_id': 'text-embedding-3-large',
        'dimension': None,  # 使用默认维度
        'base_url': None
    }
    
    openai_config = generate_complete_mcp_config(openai_text_model, openai_embedding)
    print("   ✅ OpenAI官方配置:")
    for key, value in openai_config.items():
        if 'API_KEY' in key:
            print(f"   {key}={value}")
        else:
            print(f"   {key}={value}")
    
    print("\n=== 测试完成 ===")
    print("\n📋 完整配置特点:")
    print("✅ 支持embedding_dim参数配置")
    print("✅ 根据模型类型自动设置默认维度")
    print("✅ 支持no-api-key占位符")
    print("✅ 完整的OpenAIEmbedderConfig参数支持")

def test_mcp_server_env_processing():
    """测试MCP服务器环境变量处理逻辑"""
    print("\n=== MCP服务器环境变量处理测试 ===\n")
    
    def simulate_mcp_server_logic(env_vars):
        """模拟MCP服务器的环境变量处理逻辑"""
        
        # 模拟GraphitiEmbedderConfig.from_env()的逻辑
        model_env = env_vars.get('OPENAI_EMBEDDER_MODEL_ID', '') or env_vars.get('EMBEDDER_MODEL_NAME', '')
        model = model_env if model_env.strip() else 'text-embedding-3-small'
        
        # API Key处理逻辑
        embedder_api_key = env_vars.get('OPENAI_EMBEDDER_API_KEY', '').strip()
        if embedder_api_key and embedder_api_key != 'no-api-key':
            api_key = embedder_api_key
        else:
            api_key = env_vars.get('OPENAI_API_KEY', '').strip()
        
        # 创建OpenAIEmbedderConfig的参数
        base_url = env_vars.get('OPENAI_EMBEDDER_API_URL', '').strip()
        embedding_dim = int(env_vars.get('OPENAI_EMBEDDER_DIMENSION', '1536'))
        
        config_params = {
            'api_key': api_key,
            'embedding_model': model,
            'embedding_dim': embedding_dim
        }
        
        if base_url:
            config_params['base_url'] = base_url
        
        return config_params
    
    # 测试场景1：完整的nomic-embed配置
    print("1. 完整的nomic-embed配置处理:")
    env1 = {
        'OPENAI_API_KEY': 'sk-general-key',
        'OPENAI_EMBEDDER_API_KEY': 'no-api-key',
        'OPENAI_EMBEDDER_MODEL_ID': 'nomic-embed-text',
        'OPENAI_EMBEDDER_DIMENSION': '768',
        'OPENAI_EMBEDDER_API_URL': 'http://localhost:11434/v1'
    }
    
    result1 = simulate_mcp_server_logic(env1)
    print("   ✅ 处理结果:")
    for key, value in result1.items():
        print(f"   {key}: {value}")
    
    # 验证关键逻辑
    assert result1['api_key'] == 'sk-general-key', "API Key应该回退到通用key"
    assert result1['embedding_model'] == 'nomic-embed-text', "模型ID应该正确"
    assert result1['embedding_dim'] == 768, "维度应该正确"
    print("   ✅ 验证通过：no-api-key正确回退到通用API Key")
    
    # 测试场景2：有独立API Key的配置
    print("\n2. 有独立API Key的配置处理:")
    env2 = {
        'OPENAI_API_KEY': 'sk-general-key',
        'OPENAI_EMBEDDER_API_KEY': 'sk-embed-specific-key',
        'OPENAI_EMBEDDER_MODEL_ID': 'bge-m3:latest',
        'OPENAI_EMBEDDER_DIMENSION': '1024',
        'OPENAI_EMBEDDER_API_URL': 'http://*********:11434/v1'
    }
    
    result2 = simulate_mcp_server_logic(env2)
    print("   ✅ 处理结果:")
    for key, value in result2.items():
        print(f"   {key}: {value}")
    
    assert result2['api_key'] == 'sk-embed-specific-key', "应该使用专用API Key"
    print("   ✅ 验证通过：使用专用嵌入模型API Key")
    
    print("\n📋 MCP服务器处理逻辑:")
    print("1. OPENAI_EMBEDDER_API_KEY == 'no-api-key' → 使用OPENAI_API_KEY")
    print("2. OPENAI_EMBEDDER_API_KEY 有值且不是'no-api-key' → 使用专用API Key")
    print("3. OPENAI_EMBEDDER_DIMENSION 用于设置embedding_dim参数")
    print("4. OPENAI_EMBEDDER_API_URL 用于设置base_url参数")

if __name__ == '__main__':
    test_complete_embedding_config()
    test_mcp_server_env_processing()
