"""
图谱增强服务

提供图谱增强功能的核心服务，包括：
- 框架初始化和管理
- 查询处理
- 状态监控
- 数据管理
"""

import os
import json
import asyncio
from datetime import datetime
from typing import Dict, Any, Tuple, Optional
from flask import current_app

class MockLightRAG:
    """LightRAG模拟类，用于在未安装LightRAG时提供基本功能"""

    def __init__(self, config):
        self.config = config
        self.working_dir = config.working_dir or f"./graph_storage/lightrag_{config.id}"
        self.documents = []
        self.entities = []
        self.relations = []

    async def ainsert(self, text):
        """模拟插入文档"""
        self.documents.append({
            'text': text,
            'timestamp': datetime.now().isoformat()
        })
        return True

    async def aquery(self, query, param=None):
        """模拟查询"""
        return f"模拟查询结果：针对查询 '{query}' 的回答。这是一个模拟响应，请安装lightrag-hku以获得真实的图谱增强功能。"

    def get_stats(self):
        """获取统计信息"""
        return {
            'entity_count': len(self.entities),
            'relation_count': len(self.relations),
            'document_count': len(self.documents)
        }

class GraphEnhancementService:
    """图谱增强服务类"""
    
    def __init__(self):
        self.frameworks = {}  # 存储已初始化的框架实例
        self.supported_frameworks = ['lightrag', 'graphiti', 'graphrag']
    
    def initialize_framework(self, config) -> Tuple[bool, str]:
        """初始化图谱增强框架"""
        try:
            framework = config.framework
            if framework not in self.supported_frameworks:
                return False, f"不支持的框架: {framework}"
            
            # 根据框架类型初始化
            if framework == 'lightrag':
                return self._initialize_lightrag(config)
            elif framework == 'graphiti':
                return self._initialize_graphiti(config)
            elif framework == 'graphrag':
                return self._initialize_graphrag(config)
            else:
                return False, f"框架 {framework} 尚未实现"
                
        except Exception as e:
            current_app.logger.error(f"初始化图谱增强框架失败: {e}")
            return False, f"初始化失败: {str(e)}"
    
    def _initialize_lightrag(self, config) -> Tuple[bool, str]:
        """初始化LightRAG框架"""
        try:
            # 检查LightRAG是否已安装
            try:
                import lightrag
                from lightrag import LightRAG, QueryParam
                from lightrag.llm.openai import gpt_4o_mini_complete, openai_embed
                from lightrag.utils import EmbeddingFunc
            except ImportError:
                # 如果LightRAG未安装，创建一个模拟实例用于测试
                current_app.logger.warning("LightRAG未安装，使用模拟实例")
                self.frameworks[config.id] = {
                    'type': 'lightrag',
                    'instance': MockLightRAG(config),
                    'config': config,
                    'initialized_at': datetime.now()
                }
                return True, "LightRAG模拟实例初始化成功（请安装lightrag-hku以使用完整功能）"

            # 获取配置参数
            framework_config = config.framework_config or {}
            working_dir = config.working_dir or f"./graph_storage/lightrag_{config.id}"

            # 确保工作目录存在
            os.makedirs(working_dir, exist_ok=True)

            # 创建LightRAG实例
            rag_config = {
                'working_dir': working_dir,
                'chunk_token_size': framework_config.get('chunk_token_size', 1200),
                'chunk_overlap_token_size': framework_config.get('chunk_overlap_token_size', 100),
                'enable_llm_cache': framework_config.get('enable_llm_cache', True),
                'enable_llm_cache_for_entity_extract': framework_config.get('enable_entity_cache', True)
            }

            # 配置LLM和嵌入模型
            if config.llm_config == 'inherit':
                # 使用系统配置的默认文本生成模型
                rag_config['llm_model_func'] = self._get_default_text_model_func()
            else:
                # 使用自定义配置
                # TODO: 实现自定义LLM配置
                rag_config['llm_model_func'] = self._get_default_text_model_func()

            if config.embedding_config == 'inherit':
                # 使用系统配置的默认嵌入模型
                rag_config['embedding_func'] = self._get_default_embedding_func()
            else:
                # 使用自定义配置
                # TODO: 实现自定义嵌入配置
                rag_config['embedding_func'] = self._get_default_embedding_func()

            # 创建LightRAG实例
            rag = LightRAG(**rag_config)

            # 异步初始化
            async def init_rag():
                await rag.initialize_storages()
                from lightrag.kg.shared_storage import initialize_pipeline_status
                await initialize_pipeline_status()
                return rag

            # 运行异步初始化
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                rag_instance = loop.run_until_complete(init_rag())
                self.frameworks[config.id] = {
                    'type': 'lightrag',
                    'instance': rag_instance,
                    'config': config,
                    'initialized_at': datetime.now()
                }
                return True, "LightRAG初始化成功"
            finally:
                loop.close()

        except Exception as e:
            current_app.logger.error(f"初始化LightRAG失败: {e}")
            return False, f"初始化LightRAG失败: {str(e)}"
    
    def _initialize_graphiti(self, config) -> Tuple[bool, str]:
        """初始化Graphiti框架"""
        try:
            # 检查Graphiti是否已安装
            try:
                import graphiti_core
                from graphiti_core import Graphiti
            except ImportError:
                return False, "Graphiti未安装，请先安装: pip install graphiti-core"

            # 获取配置参数
            framework_config = config.framework_config or {}

            # 图数据库配置
            database_type = framework_config.get('database_type', 'neo4j')

            if database_type == 'neo4j':
                db_uri = framework_config.get('neo4j_uri', 'bolt://localhost:7687')
                db_user = framework_config.get('neo4j_user', 'neo4j')
                db_password = framework_config.get('neo4j_password', 'password')
                database_name = framework_config.get('database_name', 'neo4j')
            elif database_type == 'falkordb':
                falkordb_host = framework_config.get('falkordb_host', 'localhost')
                falkordb_port = framework_config.get('falkordb_port', 6379)
                falkordb_password = framework_config.get('falkordb_password', '')
                # FalkorDB不需要构建URI，直接使用参数
            else:
                return False, f"不支持的数据库类型: {database_type}"

            # 获取模型配置
            text_model_config = self._get_text_model_config(framework_config)
            embedding_model_config = self._get_embedding_model_config(framework_config)

            current_app.logger.info(f"获取到的文本模型配置: {text_model_config}")

            if not text_model_config:
                return False, "未找到可用的文本生成模型配置"

            # 设置环境变量 - 重点关注必须参数，不关注供应商
            import os

            # 设置OPENAI_API_KEY（Graphiti必需）
            if text_model_config.get('api_key'):
                # 直接使用模型配置中的API Key，无论是什么提供商
                os.environ['OPENAI_API_KEY'] = text_model_config['api_key']
                current_app.logger.info(f"设置OPENAI_API_KEY使用{text_model_config.get('provider', 'Unknown')}提供商的密钥: {text_model_config['api_key'][:10]}...")
            else:
                # 如果没有API Key，设置一个默认值以绕过Graphiti的检查
                os.environ['OPENAI_API_KEY'] = 'sk-dummy-key-for-local-models'
                current_app.logger.info("模型配置中没有API Key，设置默认值以绕过检查")

            # 设置Base URL（如果有）
            if text_model_config.get('base_url'):
                os.environ['OPENAI_BASE_URL'] = text_model_config['base_url']
                current_app.logger.info(f"设置OPENAI_BASE_URL: {text_model_config['base_url']}")

            # 添加性能配置环境变量
            if framework_config.get('semaphore_limit'):
                os.environ['SEMAPHORE_LIMIT'] = str(framework_config['semaphore_limit'])
            if framework_config.get('use_parallel_runtime') is not None:
                os.environ['USE_PARALLEL_RUNTIME'] = str(framework_config['use_parallel_runtime']).lower()
            if framework_config.get('telemetry_enabled') is not None:
                os.environ['GRAPHITI_TELEMETRY_ENABLED'] = str(framework_config['telemetry_enabled']).lower()

            # 创建Graphiti实例
            if database_type == 'neo4j':
                # Neo4j使用标准构造函数
                graphiti = Graphiti(db_uri, db_user, db_password)
            elif database_type == 'falkordb':
                # FalkorDB需要使用专门的FalkorDriver
                try:
                    from graphiti_core.driver.falkordb_driver import FalkorDriver

                    # 创建FalkorDB驱动
                    falkor_driver = FalkorDriver(
                        host=falkordb_host,
                        port=falkordb_port,
                        username=framework_config.get('falkordb_username'),  # 可选
                        password=falkordb_password if falkordb_password else None,
                        database=framework_config.get('falkordb_graph_name', 'default_graph')  # 图名称
                    )

                    # 使用graph_driver参数传递驱动
                    graphiti = Graphiti(graph_driver=falkor_driver)

                except ImportError:
                    return False, "FalkorDB驱动未安装，请确保安装了完整的graphiti-core[falkordb]"
            else:
                return False, f"不支持的数据库类型: {database_type}"

            # 测试连接
            try:
                # 简单的连接测试
                # TODO: 添加更完整的连接测试逻辑
                pass
            except Exception as e:
                return False, f"Graphiti连接测试失败: {str(e)}"

            self.frameworks[config.id] = {
                'type': 'graphiti',
                'instance': graphiti,
                'config': config,
                'text_model_config': text_model_config,
                'embedding_model_config': embedding_model_config,
                'initialized_at': datetime.now()
            }

            current_app.logger.info(f"Graphiti初始化成功，使用文本模型: {text_model_config.get('name', 'Unknown')}")
            return True, "Graphiti初始化成功"

        except Exception as e:
            current_app.logger.error(f"初始化Graphiti失败: {e}")
            return False, f"初始化Graphiti失败: {str(e)}"
    
    def _initialize_graphrag(self, config) -> Tuple[bool, str]:
        """初始化GraphRAG框架"""
        try:
            # TODO: 实现GraphRAG初始化
            return False, "GraphRAG框架尚未实现"
            
        except Exception as e:
            current_app.logger.error(f"初始化GraphRAG失败: {e}")
            return False, f"初始化GraphRAG失败: {str(e)}"
    
    def get_status(self, config) -> Dict[str, Any]:
        """获取图谱增强状态"""
        try:
            framework_info = self.frameworks.get(config.id)
            if not framework_info:
                return {
                    'enabled': config.enabled,
                    'framework': config.framework,
                    'status': 'not_initialized',
                    'message': '框架未初始化'
                }
            
            # 根据框架类型获取状态
            if framework_info['type'] == 'lightrag':
                return self._get_lightrag_status(framework_info)
            elif framework_info['type'] == 'graphiti':
                return self._get_graphiti_status(framework_info)
            elif framework_info['type'] == 'graphrag':
                return self._get_graphrag_status(framework_info)
            else:
                return {
                    'enabled': config.enabled,
                    'framework': config.framework,
                    'status': 'unknown',
                    'message': '未知框架类型'
                }
                
        except Exception as e:
            current_app.logger.error(f"获取图谱增强状态失败: {e}")
            return {
                'enabled': False,
                'status': 'error',
                'message': f'获取状态失败: {str(e)}'
            }
    
    def _get_lightrag_status(self, framework_info) -> Dict[str, Any]:
        """获取LightRAG状态"""
        try:
            rag = framework_info['instance']
            config = framework_info['config']
            
            # 获取统计信息
            working_dir = config.working_dir or f"./graph_storage/lightrag_{config.id}"
            
            # 统计实体和关系数量
            entity_count = 0
            relation_count = 0
            
            # TODO: 从LightRAG实例获取实际统计信息
            
            return {
                'enabled': True,
                'framework': 'lightrag',
                'status': 'ready',
                'connected': True,
                'indexed': True,
                'statistics': {
                    'entity_count': entity_count,
                    'relation_count': relation_count,
                    'document_count': 0,  # TODO: 获取实际文档数量
                    'last_update': framework_info['initialized_at'].isoformat()
                },
                'working_dir': working_dir
            }
            
        except Exception as e:
            return {
                'enabled': True,
                'framework': 'lightrag',
                'status': 'error',
                'message': f'获取LightRAG状态失败: {str(e)}'
            }
    
    def _get_graphiti_status(self, framework_info) -> Dict[str, Any]:
        """获取Graphiti状态"""
        try:
            graphiti = framework_info['instance']
            config = framework_info['config']
            
            # TODO: 从Graphiti实例获取状态信息
            
            return {
                'enabled': True,
                'framework': 'graphiti',
                'status': 'ready',
                'connected': True,
                'indexed': True,
                'statistics': {
                    'entity_count': 0,
                    'relation_count': 0,
                    'episode_count': 0,
                    'last_update': framework_info['initialized_at'].isoformat()
                }
            }
            
        except Exception as e:
            return {
                'enabled': True,
                'framework': 'graphiti',
                'status': 'error',
                'message': f'获取Graphiti状态失败: {str(e)}'
            }
    
    def _get_graphrag_status(self, framework_info) -> Dict[str, Any]:
        """获取GraphRAG状态"""
        # TODO: 实现GraphRAG状态获取
        return {
            'enabled': True,
            'framework': 'graphrag',
            'status': 'not_implemented',
            'message': 'GraphRAG状态获取尚未实现'
        }
    
    def query(self, config, query: str, params: Dict[str, Any]) -> Tuple[bool, Any]:
        """执行图谱增强查询"""
        try:
            framework_info = self.frameworks.get(config.id)
            if not framework_info:
                return False, "框架未初始化"
            
            # 根据框架类型执行查询
            if framework_info['type'] == 'lightrag':
                return self._query_lightrag(framework_info, query, params)
            elif framework_info['type'] == 'graphiti':
                return self._query_graphiti(framework_info, query, params)
            elif framework_info['type'] == 'graphrag':
                return self._query_graphrag(framework_info, query, params)
            else:
                return False, f"不支持的框架类型: {framework_info['type']}"
                
        except Exception as e:
            current_app.logger.error(f"执行图谱增强查询失败: {e}")
            return False, f"查询失败: {str(e)}"
    
    def _query_lightrag(self, framework_info, query: str, params: Dict[str, Any]) -> Tuple[bool, Any]:
        """执行LightRAG查询"""
        try:
            rag = framework_info['instance']
            
            # 构建查询参数
            from lightrag import QueryParam
            query_param = QueryParam(
                mode=params.get('mode', 'hybrid'),
                top_k=params.get('top_k', 60),
                chunk_top_k=params.get('chunk_top_k', 10),
                response_type=params.get('response_type', 'Multiple Paragraphs')
            )
            
            # 执行异步查询
            async def run_query():
                return await rag.aquery(query, param=query_param)
            
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                result = loop.run_until_complete(run_query())
                return True, result
            finally:
                loop.close()
                
        except Exception as e:
            current_app.logger.error(f"LightRAG查询失败: {e}")
            return False, f"LightRAG查询失败: {str(e)}"
    
    def _query_graphiti(self, framework_info, query: str, params: Dict[str, Any]) -> Tuple[bool, Any]:
        """执行Graphiti查询"""
        try:
            # TODO: 实现Graphiti查询
            return False, "Graphiti查询尚未实现"
            
        except Exception as e:
            current_app.logger.error(f"Graphiti查询失败: {e}")
            return False, f"Graphiti查询失败: {str(e)}"
    
    def _query_graphrag(self, framework_info, query: str, params: Dict[str, Any]) -> Tuple[bool, Any]:
        """执行GraphRAG查询"""
        try:
            # TODO: 实现GraphRAG查询
            return False, "GraphRAG查询尚未实现"
            
        except Exception as e:
            current_app.logger.error(f"GraphRAG查询失败: {e}")
            return False, f"GraphRAG查询失败: {str(e)}"
    
    def test_connection(self, framework: str, framework_config: Dict[str, Any]) -> Tuple[bool, str]:
        """测试框架连接"""
        try:
            if framework == 'lightrag':
                return self._test_lightrag_connection(framework_config)
            elif framework == 'graphiti':
                return self._test_graphiti_connection(framework_config)
            elif framework == 'graphrag':
                return self._test_graphrag_connection(framework_config)
            else:
                return False, f"不支持的框架: {framework}"
                
        except Exception as e:
            current_app.logger.error(f"测试框架连接失败: {e}")
            return False, f"连接测试失败: {str(e)}"
    
    def _test_lightrag_connection(self, config: Dict[str, Any]) -> Tuple[bool, str]:
        """测试LightRAG连接"""
        try:
            # LightRAG主要依赖文件系统，测试工作目录是否可写
            working_dir = config.get('working_dir', './test_lightrag')
            os.makedirs(working_dir, exist_ok=True)
            
            # 测试写入权限
            test_file = os.path.join(working_dir, 'test_connection.txt')
            with open(test_file, 'w') as f:
                f.write('test')
            os.remove(test_file)
            
            return True, "LightRAG连接测试成功"
            
        except Exception as e:
            return False, f"LightRAG连接测试失败: {str(e)}"
    
    def _test_graphiti_connection(self, config: Dict[str, Any]) -> Tuple[bool, str]:
        """测试Graphiti连接"""
        try:
            # 检查Graphiti是否已安装
            try:
                import graphiti_core
                from graphiti_core import Graphiti
            except ImportError:
                return False, "Graphiti未安装，请先安装: pip install graphiti-core"

            # 获取图数据库配置
            database_type = config.get('database_type', 'neo4j')

            if database_type == 'neo4j':
                db_uri = config.get('neo4j_uri', 'bolt://localhost:7687')
                db_user = config.get('neo4j_user', 'neo4j')
                db_password = config.get('neo4j_password', 'password')
                database_name = config.get('database_name', 'neo4j')
            elif database_type == 'falkordb':
                falkordb_host = config.get('falkordb_host', 'localhost')
                falkordb_port = config.get('falkordb_port', 6379)
                falkordb_password = config.get('falkordb_password', '')
            else:
                return False, f"不支持的数据库类型: {database_type}"

            # 获取模型配置
            text_model_config = self._get_text_model_config(config)
            if not text_model_config:
                return False, "未找到可用的文本生成模型配置，请在系统中配置默认文本生成模型"

            # 设置临时环境变量进行测试
            import os
            original_env = {}
            try:
                # 设置OPENAI_API_KEY（Graphiti必需）- 重点关注必须参数，不关注供应商
                original_env['OPENAI_API_KEY'] = os.environ.get('OPENAI_API_KEY')
                if text_model_config.get('api_key'):
                    # 直接使用模型配置中的API Key，无论是什么提供商
                    os.environ['OPENAI_API_KEY'] = text_model_config['api_key']
                else:
                    os.environ['OPENAI_API_KEY'] = 'sk-dummy-key-for-local-models'

                # 设置Base URL（如果有）
                if text_model_config.get('base_url'):
                    original_env['OPENAI_BASE_URL'] = os.environ.get('OPENAI_BASE_URL')
                    os.environ['OPENAI_BASE_URL'] = text_model_config['base_url']

                # 测试图数据库连接
                try:
                    if database_type == 'neo4j':
                        from neo4j import GraphDatabase
                        driver = GraphDatabase.driver(db_uri, auth=(db_user, db_password))
                        with driver.session(database=database_name) as session:
                            result = session.run("RETURN 1 as test")
                            test_value = result.single()["test"]
                            if test_value != 1:
                                return False, "Neo4j连接测试失败"
                        driver.close()
                    elif database_type == 'falkordb':
                        import redis
                        # 测试Redis连接
                        if falkordb_password:
                            r = redis.Redis(host=falkordb_host, port=falkordb_port,
                                          password=falkordb_password)
                        else:
                            r = redis.Redis(host=falkordb_host, port=falkordb_port)
                        # 测试连接
                        r.ping()
                        # 测试FalkorDB图功能（如果可用）
                        try:
                            r.execute_command("GRAPH.QUERY", "test_graph", "RETURN 1")
                        except Exception as e:
                            if "unknown command" in str(e).lower():
                                return False, "FalkorDB图模块未加载，请确保安装了FalkorDB"
                            # 其他错误可能是正常的（如图不存在）
                except Exception as e:
                    return False, f"{database_type}连接失败: {str(e)}"

                # 测试Graphiti初始化
                try:
                    if database_type == 'neo4j':
                        graphiti = Graphiti(db_uri, db_user, db_password)
                    elif database_type == 'falkordb':
                        # 使用FalkorDriver
                        from graphiti_core.driver.falkordb_driver import FalkorDriver

                        falkor_driver = FalkorDriver(
                            host=falkordb_host,
                            port=falkordb_port,
                            username=config.get('falkordb_username'),  # 可选
                            password=falkordb_password if falkordb_password else None,
                            database=config.get('falkordb_graph_name', 'default_graph')
                        )

                        graphiti = Graphiti(graph_driver=falkor_driver)
                    # 简单测试
                    # TODO: 添加更完整的Graphiti功能测试
                except Exception as e:
                    return False, f"Graphiti初始化失败: {str(e)}"

                return True, f"Graphiti连接测试成功，数据库类型: {database_type}，使用文本模型: {text_model_config['name']}"

            finally:
                # 恢复原始环境变量
                for key, value in original_env.items():
                    if value is None:
                        os.environ.pop(key, None)
                    else:
                        os.environ[key] = value

        except Exception as e:
            return False, f"Graphiti连接测试失败: {str(e)}"
    
    def _test_graphrag_connection(self, config: Dict[str, Any]) -> Tuple[bool, str]:
        """测试GraphRAG连接"""
        try:
            # TODO: 实现GraphRAG连接测试
            return False, "GraphRAG连接测试尚未实现"
            
        except Exception as e:
            return False, f"GraphRAG连接测试失败: {str(e)}"
    
    def rebuild_index(self, config) -> Tuple[bool, str]:
        """重建图谱增强索引"""
        try:
            framework_info = self.frameworks.get(config.id)
            if not framework_info:
                return False, "框架未初始化"
            
            # TODO: 实现索引重建逻辑
            return True, "索引重建成功"
            
        except Exception as e:
            current_app.logger.error(f"重建图谱增强索引失败: {e}")
            return False, f"重建索引失败: {str(e)}"
    
    def clear_data(self, config) -> Tuple[bool, str]:
        """清空图谱增强数据"""
        try:
            framework_info = self.frameworks.get(config.id)
            if not framework_info:
                return False, "框架未初始化"

            # TODO: 实现数据清空逻辑
            return True, "数据清空成功"

        except Exception as e:
            current_app.logger.error(f"清空图谱增强数据失败: {e}")
            return False, f"清空数据失败: {str(e)}"

    def add_documents(self, config, documents: list) -> Tuple[bool, str]:
        """添加文档到图谱增强系统"""
        try:
            framework_info = self.frameworks.get(config.id)
            if not framework_info:
                return False, "框架未初始化"

            # 根据框架类型添加文档
            if framework_info['type'] == 'lightrag':
                return self._add_documents_lightrag(framework_info, documents)
            elif framework_info['type'] == 'graphiti':
                return self._add_documents_graphiti(framework_info, documents)
            elif framework_info['type'] == 'graphrag':
                return self._add_documents_graphrag(framework_info, documents)
            else:
                return False, f"不支持的框架类型: {framework_info['type']}"

        except Exception as e:
            current_app.logger.error(f"添加文档失败: {e}")
            return False, f"添加文档失败: {str(e)}"

    def _add_documents_lightrag(self, framework_info, documents: list) -> Tuple[bool, str]:
        """向LightRAG添加文档"""
        try:
            rag = framework_info['instance']

            # 合并所有文档内容
            combined_text = "\n\n".join(documents)

            # 异步插入文档
            async def insert_docs():
                await rag.ainsert(combined_text)

            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                loop.run_until_complete(insert_docs())
                return True, f"成功添加 {len(documents)} 个文档到LightRAG"
            finally:
                loop.close()

        except Exception as e:
            current_app.logger.error(f"LightRAG添加文档失败: {e}")
            return False, f"LightRAG添加文档失败: {str(e)}"

    def _add_documents_graphiti(self, framework_info, documents: list) -> Tuple[bool, str]:
        """向Graphiti添加文档"""
        try:
            # TODO: 实现Graphiti文档添加
            return False, "Graphiti文档添加尚未实现"

        except Exception as e:
            current_app.logger.error(f"Graphiti添加文档失败: {e}")
            return False, f"Graphiti添加文档失败: {str(e)}"

    def _add_documents_graphrag(self, framework_info, documents: list) -> Tuple[bool, str]:
        """向GraphRAG添加文档"""
        try:
            # TODO: 实现GraphRAG文档添加
            return False, "GraphRAG文档添加尚未实现"

        except Exception as e:
            current_app.logger.error(f"GraphRAG添加文档失败: {e}")
            return False, f"GraphRAG添加文档失败: {str(e)}"

    def _get_default_text_model_func(self):
        """获取默认文本生成模型函数"""
        try:
            from app.models import ModelConfig
            from lightrag.llm import gpt_4o_mini_complete

            # 获取默认文本生成模型
            default_model = ModelConfig.query.filter_by(is_default_text=True).first()
            if not default_model:
                # 如果没有设置默认文本生成模型，查找第一个支持文本输出的模型
                text_models = ModelConfig.query.filter(
                    ModelConfig.modalities.contains('text_output')
                ).all()
                if text_models:
                    default_model = text_models[0]
                else:
                    # 最后回退到第一个可用模型
                    default_model = ModelConfig.query.first()

            if default_model:
                current_app.logger.info(f"图谱增强使用文本生成模型: {default_model.name}")
                # TODO: 根据不同的模型配置返回相应的模型函数
                # 目前先返回默认的gpt_4o_mini_complete，后续可以根据模型类型动态选择
                return gpt_4o_mini_complete
            else:
                current_app.logger.warning("未找到可用的文本生成模型，使用默认模型")
                return gpt_4o_mini_complete

        except Exception as e:
            current_app.logger.error(f"获取默认文本生成模型失败: {e}")
            return gpt_4o_mini_complete

    def _get_default_embedding_func(self):
        """获取默认嵌入模型函数"""
        try:
            from app.models import ModelConfig
            from lightrag.base import EmbeddingFunc
            from lightrag.llm import openai_embed

            # 获取默认嵌入模型
            default_model = ModelConfig.query.filter_by(is_default_embedding=True).first()

            # 如果没有默认嵌入模型，查找第一个支持向量输出的模型
            if not default_model:
                embedding_models = ModelConfig.query.filter(
                    ModelConfig.modalities.contains('vector_output')
                ).all()
                if embedding_models:
                    default_model = embedding_models[0]

            if default_model:
                current_app.logger.info(f"图谱增强使用嵌入模型: {default_model.name}")
                # TODO: 根据不同的嵌入模型配置返回相应的嵌入函数
                # 目前先返回默认的openai_embed，后续可以根据模型类型动态选择
                return EmbeddingFunc(
                    embedding_dim=1536,
                    max_token_size=8192,
                    func=openai_embed
                )
            else:
                current_app.logger.warning("未找到可用的嵌入模型，使用默认嵌入函数")
                return EmbeddingFunc(
                    embedding_dim=1536,
                    max_token_size=8192,
                    func=openai_embed
                )

        except Exception as e:
            current_app.logger.error(f"获取默认嵌入模型失败: {e}")
            return EmbeddingFunc(
                embedding_dim=1536,
                max_token_size=8192,
                func=openai_embed
            )

    def _get_text_model_config(self, framework_config: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """获取文本生成模型配置"""
        try:
            from app.models import ModelConfig

            text_model_type = framework_config.get('text_model_type', 'default')
            text_model_id = framework_config.get('text_model_id')

            current_app.logger.info(f"获取文本模型配置: type={text_model_type}, id={text_model_id}")

            if text_model_type == 'default' or text_model_id == 'default':
                # 使用默认文本生成模型
                model = ModelConfig.query.filter_by(is_default_text=True).first()
                current_app.logger.info(f"查找默认文本生成模型: {model.name if model else 'None'}")
                if not model:
                    # 查找第一个支持文本输出的模型
                    text_models = ModelConfig.query.filter(
                        ModelConfig.modalities.contains('text_output')
                    ).all()
                    current_app.logger.info(f"查找支持文本输出的模型: {len(text_models)} 个")
                    if text_models:
                        model = text_models[0]
                        current_app.logger.info(f"使用第一个文本输出模型: {model.name}")
                    else:
                        model = ModelConfig.query.first()
                        current_app.logger.info(f"使用第一个可用模型: {model.name if model else 'None'}")
            else:
                # 使用指定的模型
                model = ModelConfig.query.get(text_model_id)
                current_app.logger.info(f"使用指定模型: {model.name if model else 'None'}")

            if model:
                config = {
                    'id': model.id,
                    'name': model.name,
                    'provider': model.provider,
                    'model_id': model.model_id,
                    'api_key': model.api_key,
                    'base_url': model.base_url,
                    'context_window': model.context_window,
                    'max_output_tokens': model.max_output_tokens
                }
                current_app.logger.info(f"返回模型配置: {model.name}, provider={model.provider}, has_api_key={bool(model.api_key)}")
                return config

            current_app.logger.warning("未找到任何可用的文本生成模型")
            return None

        except Exception as e:
            current_app.logger.error(f"获取文本生成模型配置失败: {e}")
            return None

    def _get_embedding_model_config(self, framework_config: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """获取嵌入模型配置"""
        try:
            from app.models import ModelConfig

            embedding_model_type = framework_config.get('embedding_model_type', 'default')
            embedding_model_id = framework_config.get('embedding_model_id')

            if embedding_model_type == 'default' or embedding_model_id == 'default':
                # 使用默认嵌入模型
                model = ModelConfig.query.filter_by(is_default_embedding=True).first()
                if not model:
                    # 查找第一个支持向量输出的模型
                    embedding_models = ModelConfig.query.filter(
                        ModelConfig.modalities.contains('vector_output')
                    ).all()
                    if embedding_models:
                        model = embedding_models[0]
            else:
                # 使用指定的模型
                model = ModelConfig.query.get(embedding_model_id)

            if model:
                return {
                    'id': model.id,
                    'name': model.name,
                    'provider': model.provider,
                    'model_id': model.model_id,
                    'api_key': model.api_key,
                    'base_url': model.base_url
                }

            return None

        except Exception as e:
            current_app.logger.error(f"获取嵌入模型配置失败: {e}")
            return None
