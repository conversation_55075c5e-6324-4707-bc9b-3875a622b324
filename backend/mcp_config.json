{"mcpServers": {"code-sandbox-mcp": {"args": [], "comm_type": "stdio", "command": "third_mcp_server/code-sandbox-mcp/code-sandbox-mcp-darwin-arm64", "description": "", "enabled": false, "env": {}, "internal": false}, "context7": {"args": ["-y", "@upstash/context7-mcp"], "command": "npx", "enabled": false}, "filesystem": {"args": ["-y", "@modelcontextprotocol/server-filesystem", "./agent-workspace/"], "comm_type": "stdio", "command": "npx", "description": "", "enabled": true, "internal": false}, "graphiti-memory": {"description": "", "internal": false, "comm_type": "http", "enabled": false, "url": "http://localhost:8000/sse", "env": {}}, "knowledge-base": {"args": ["-s", "-X", "POST", "http://localhost:8080/api/mcp/knowledge-base"], "comm_type": "stdio", "command": "curl", "description": "统一知识库查询服务（支持Dify、RAGFlow等）", "enabled": true, "env": {}, "internal": true}, "markmap": {"args": ["-y", "@jinzcdev/markmap-mcp-server", "./agent-markmap/"], "comm_type": "stdio", "command": "npx", "enabled": false, "internal": false}, "mcp-server-browser": {"args": ["@agent-infra/mcp-server-browser@latest"], "comm_type": "stdio", "command": "npx", "enabled": false, "env": {}, "internal": false}, "mcp-server-fetch": {"args": ["-m", "mcp_server_fetch"], "comm_type": "stdio", "command": "python", "description": "", "enabled": false, "env": {}, "internal": false}, "memory": {"args": ["-y", "@modelcontextprotocol/server-memory"], "comm_type": "stdio", "command": "npx", "description": "智能体记忆能力（基于向量数据库的未来功能）", "enabled": false, "env": {}, "internal": false}, "metatrader-mcp": {"comm_type": "http", "description": "MetaTrader5 MCP", "enabled": false, "internal": false, "url": "http://*********:8888/openapi.json"}, "milvus": {"args": ["run", "third/mcp-server-milvus/src/mcp_server_milvus/server.py", "--mil<PERSON><PERSON>-<PERSON>ri", "http://localhost:19530"], "comm_type": "stdio", "command": "uv", "description": "<PERSON><PERSON><PERSON><PERSON>向量数据库", "enabled": false, "internal": false}, "playwright": {"args": ["@playwright/mcp@latest"], "comm_type": "stdio", "command": "npx", "enabled": true, "env": {}, "internal": false}, "ragflow": {"comm_type": "http", "description": "", "enabled": false, "env": {}, "internal": false, "url": "http://*********:9383/sse"}, "searxng": {"args": ["-y", "mcp-searxng"], "comm_type": "stdio", "command": "npx", "description": "SearXNG联网搜索服务器", "enabled": true, "env": {"SEARXNG_URL": "http://*********:5080"}, "internal": false}, "sequential-thinking": {"args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "comm_type": "stdio", "command": "npx", "description": "深度连续思考", "enabled": true, "env": {}, "internal": false}, "variables-server": {"args": ["-s", "-X", "POST", "http://localhost:8080/api/mcp/variables"], "comm_type": "stdio", "command": "curl", "description": "环境变量和智能体变量MCP服务器", "enabled": true, "env": {}, "internal": true}, "服务器健康": {"comm_type": "http", "description": "", "enabled": false, "env": {}, "internal": false, "url": "http://*********:8090/api/v1/mcp/health-monitor/openapi.json"}}}